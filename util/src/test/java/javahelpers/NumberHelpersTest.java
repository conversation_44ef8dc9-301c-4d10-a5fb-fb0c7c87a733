package javahelpers;

import javaHelpers.NumberHelpers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class NumberHelpersTest {

    @Test
    void getNumberBitsNeeded() {
        byte result = NumberHelpers.getNumberBitsNeeded(0, 0);
        Assertions.assertEquals(0,result);

        result = NumberHelpers.getNumberBitsNeeded(0, 1);
        Assertions.assertEquals(1,result);

        result = NumberHelpers.getNumberBitsNeeded(0, 2);
        Assertions.assertEquals(2,result);

        result = NumberHelpers.getNumberBitsNeeded(0, 3);
        Assertions.assertEquals(2,result);

        result = NumberHelpers.getNumberBitsNeeded(0, 4);
        Assertions.assertEquals(3,result);
    }

    @Test
    void getNumberBitsNeededOverflowCases() {
        // Test case where range would overflow
        // Long.MIN_VALUE to Long.MAX_VALUE spans more than Long.MAX_VALUE
        byte result = NumberHelpers.getNumberBitsNeeded(Long.MIN_VALUE, Long.MAX_VALUE);
        Assertions.assertEquals(64, result, "Range from Long.MIN_VALUE to Long.MAX_VALUE should need 64 bits");

        // Test case with large positive range that would overflow
        result = NumberHelpers.getNumberBitsNeeded(-1, Long.MAX_VALUE);
        Assertions.assertEquals(64, result, "Range from -1 to Long.MAX_VALUE should need 64 bits");

        // Test case that's just under the overflow threshold
        result = NumberHelpers.getNumberBitsNeeded(0, Long.MAX_VALUE);
        Assertions.assertEquals(63, result, "Range from 0 to Long.MAX_VALUE should need 63 bits");

        // Test case with negative numbers that don't overflow
        result = NumberHelpers.getNumberBitsNeeded(-100, -1);
        Assertions.assertEquals(7, result, "Range from -100 to -1 should need 7 bits (99 values)");

        // Test large positive range that doesn't overflow
        result = NumberHelpers.getNumberBitsNeeded(1000000, 1000000 + 255);
        Assertions.assertEquals(8, result, "Range of 256 values should need 8 bits");
    }

    @Test
    void getNumberBytesNeeded() {
        // Test basic cases
        short result = NumberHelpers.getNumberBytesNeeded(0, 0);
        Assertions.assertEquals(1, result, "Equal values should need 1 byte");

        result = NumberHelpers.getNumberBytesNeeded(0, 255);
        Assertions.assertEquals(1, result, "Range of 256 values should need 1 byte");

        result = NumberHelpers.getNumberBytesNeeded(0, 256);
        Assertions.assertEquals(2, result, "Range of 257 values should need 2 bytes");

        // Test overflow cases
        result = NumberHelpers.getNumberBytesNeeded(Long.MIN_VALUE, Long.MAX_VALUE);
        Assertions.assertEquals(8, result, "Range from Long.MIN_VALUE to Long.MAX_VALUE should need 8 bytes");

        result = NumberHelpers.getNumberBytesNeeded(-1, Long.MAX_VALUE);
        Assertions.assertEquals(8, result, "Range from -1 to Long.MAX_VALUE should need 8 bytes");
    }

    @Test
    void testMaskLS() {
        // Test edge case: 0 bits - due to shift behavior (>>> 64 = >>> 0), returns all 1s
        long result = NumberHelpers.maskLS((byte) 0);
        Assertions.assertEquals(0L, result, "maskLS(0) should return -1 (all bits set due to shift behavior)");

        // Test special edge case: 64 bits (Long.SIZE) - should return 0 according to implementation
        result = NumberHelpers.maskLS((byte) 64);
        Assertions.assertEquals(-1L, result, "maskLS(64) should return 0 (special case)");

        // Test edge case: 1 bit - should return 1 (binary: ...0001)
        result = NumberHelpers.maskLS((byte) 1);
        Assertions.assertEquals(1L, result, "maskLS(1) should return 1");

        // Test typical case: 2 bits - should return 3 (binary: ...0011)
        result = NumberHelpers.maskLS((byte) 2);
        Assertions.assertEquals(3L, result, "maskLS(2) should return 3");

        // Test typical case: 4 bits - should return 15 (binary: ...1111)
        result = NumberHelpers.maskLS((byte) 4);
        Assertions.assertEquals(15L, result, "maskLS(4) should return 15");

        // Test typical case: 8 bits - should return 255 (binary: ...11111111)
        result = NumberHelpers.maskLS((byte) 8);
        Assertions.assertEquals(255L, result, "maskLS(8) should return 255");

        // Test larger case: 16 bits - should return 65535
        result = NumberHelpers.maskLS((byte) 16);
        Assertions.assertEquals(65535L, result, "maskLS(16) should return 65535");

        // Test near-max case: 63 bits - should return Long.MAX_VALUE
        result = NumberHelpers.maskLS((byte) 63);
        Assertions.assertEquals(Long.MAX_VALUE, result, "maskLS(63) should return Long.MAX_VALUE");

    }

    @Test
    void testMaskMS() {
        // Test edge case: 0 bits - should return 0
        long result = NumberHelpers.maskMS((byte) 0);
        Assertions.assertEquals(0L, result, "maskMS(0) should return 0");

        // Test max case: 64 bits - should set all bits (all 1s)
        result = NumberHelpers.maskMS((byte) 64);
        Assertions.assertEquals(-1L, result, "maskMS(64) should return -1 (all bits set)");

        // Test edge case: 1 bit - should set only the MSB (sign bit)
        result = NumberHelpers.maskMS((byte) 1);
        Assertions.assertEquals(Long.MIN_VALUE, result, "maskMS(1) should return Long.MIN_VALUE");

        // Test typical case: 2 bits - should set top 2 bits
        result = NumberHelpers.maskMS((byte) 2);
        long expected2 = 0xC000_0000_0000_0000L; // Top 2 bits set
        Assertions.assertEquals(expected2, result, "maskMS(2) should set top 2 bits");

        // Test typical case: 4 bits - should set top 4 bits
        result = NumberHelpers.maskMS((byte) 4);
        long expected4 = 0xF000_0000_0000_0000L; // Top 4 bits set
        Assertions.assertEquals(expected4, result, "maskMS(4) should set top 4 bits");

        // Test typical case: 8 bits - should set top byte
        result = NumberHelpers.maskMS((byte) 8);
        long expected8 = 0xFF00_0000_0000_0000L; // Top byte set
        Assertions.assertEquals(expected8, result, "maskMS(8) should set top byte");

        // Test larger case: 16 bits - should set top 2 bytes
        result = NumberHelpers.maskMS((byte) 16);
        long expected16 = 0xFFFF_0000_0000_0000L; // Top 2 bytes set
        Assertions.assertEquals(expected16, result, "maskMS(16) should set top 2 bytes");

        // Test near-max case: 63 bits - should set all but LSB
        result = NumberHelpers.maskMS((byte) 63);
        long expected63 = 0xFFFF_FFFF_FFFF_FFFEL; // All bits except LSB
        Assertions.assertEquals(expected63, result, "maskMS(63) should set all but LSB");


    }

    @Test
    void testZeroMaskLS() {
        // Test edge case: 0 bits - should return all 1s (no bits zeroed)
        long result = NumberHelpers.zeroMaskLS((byte) 0);
        Assertions.assertEquals(-1L, result, "zeroMaskLS(0) should return -1 (all bits set)");

        // Test max case: 64 bits - due to shift behavior (<< 64 = << 0), returns all 1s
        result = NumberHelpers.zeroMaskLS((byte) 64);
        Assertions.assertEquals(-0L, result, "zeroMaskLS(64) should return -1 (all bits set due to shift behavior)");

        // Test edge case: 1 bit - should zero only the LSB
        result = NumberHelpers.zeroMaskLS((byte) 1);
        long expected1 = 0xFFFF_FFFF_FFFF_FFFEL; // All bits except LSB
        Assertions.assertEquals(expected1, result, "zeroMaskLS(1) should zero only LSB");

        // Test typical case: 2 bits - should zero bottom 2 bits
        result = NumberHelpers.zeroMaskLS((byte) 2);
        long expected2 = 0xFFFF_FFFF_FFFF_FFFCL; // All bits except bottom 2
        Assertions.assertEquals(expected2, result, "zeroMaskLS(2) should zero bottom 2 bits");

        // Test typical case: 4 bits - should zero bottom 4 bits
        result = NumberHelpers.zeroMaskLS((byte) 4);
        long expected4 = 0xFFFF_FFFF_FFFF_FFF0L; // All bits except bottom 4
        Assertions.assertEquals(expected4, result, "zeroMaskLS(4) should zero bottom 4 bits");

        // Test typical case: 8 bits - should zero bottom byte
        result = NumberHelpers.zeroMaskLS((byte) 8);
        long expected8 = 0xFFFF_FFFF_FFFF_FF00L; // All bits except bottom byte
        Assertions.assertEquals(expected8, result, "zeroMaskLS(8) should zero bottom byte");

        // Test larger case: 16 bits - should zero bottom 2 bytes
        result = NumberHelpers.zeroMaskLS((byte) 16);
        long expected16 = 0xFFFF_FFFF_FFFF_0000L; // All bits except bottom 2 bytes
        Assertions.assertEquals(expected16, result, "zeroMaskLS(16) should zero bottom 2 bytes");

        // Test near-max case: 63 bits - should zero all but MSB
        result = NumberHelpers.zeroMaskLS((byte) 63);
        long expected63 = 0x8000_0000_0000_0000L; // Only MSB set
        Assertions.assertEquals(expected63, result, "zeroMaskLS(63) should zero all but MSB");

    }

    @Test
    void testZeroMaskMS() {
        // Test edge case: 0 bits - should return all 1s (no bits zeroed)
        long result = NumberHelpers.zeroMaskMS((byte) 0);
        Assertions.assertEquals(-1L, result, "zeroMaskMS(0) should return -1 (all bits set)");

        // Test max case: 64 bits - due to shift behavior (>>> 64 = >>> 0), returns all 1s
        result = NumberHelpers.zeroMaskMS((byte) 64);
        Assertions.assertEquals(-0, result, "zeroMaskMS(64) should return -1 (all bits set due to shift behavior)");

        // Test edge case: 1 bit - should zero only the MSB
        result = NumberHelpers.zeroMaskMS((byte) 1);
        Assertions.assertEquals(Long.MAX_VALUE, result, "zeroMaskMS(1) should zero only MSB");

        // Test typical case: 2 bits - should zero top 2 bits
        result = NumberHelpers.zeroMaskMS((byte) 2);
        long expected2 = 0x3FFF_FFFF_FFFF_FFFFL; // All bits except top 2
        Assertions.assertEquals(expected2, result, "zeroMaskMS(2) should zero top 2 bits");

        // Test typical case: 4 bits - should zero top 4 bits
        result = NumberHelpers.zeroMaskMS((byte) 4);
        long expected4 = 0x0FFF_FFFF_FFFF_FFFFL; // All bits except top 4
        Assertions.assertEquals(expected4, result, "zeroMaskMS(4) should zero top 4 bits");

        // Test typical case: 8 bits - should zero top byte
        result = NumberHelpers.zeroMaskMS((byte) 8);
        long expected8 = 0x00FF_FFFF_FFFF_FFFFL; // All bits except top byte
        Assertions.assertEquals(expected8, result, "zeroMaskMS(8) should zero top byte");

        // Test larger case: 16 bits - should zero top 2 bytes
        result = NumberHelpers.zeroMaskMS((byte) 16);
        long expected16 = 0x0000_FFFF_FFFF_FFFFL; // All bits except top 2 bytes (281474976710655L)
        Assertions.assertEquals(281474976710655L, result, "zeroMaskMS(16) should zero top 2 bytes");

        // Test near-max case: 63 bits - should zero all but LSB
        result = NumberHelpers.zeroMaskMS((byte) 63);
        Assertions.assertEquals(1L, result, "zeroMaskMS(63) should zero all but LSB");
    }



    @Test
    void mask() {
        long mask = NumberHelpers.maskLS((byte) 2);
        mask = NumberHelpers.maskLS((byte) 0);

        mask = NumberHelpers.maskMS((byte) 0);
        mask = NumberHelpers.maskMS((byte) 1);
        mask = NumberHelpers.maskMS((byte) 2);
        mask = NumberHelpers.maskLSMS((byte) 3, (byte) 4);

        mask = NumberHelpers.zeroMaskMS((byte) 2);
        mask = NumberHelpers.zeroMaskLS((byte) 2);

        long result = NumberHelpers.shiftLS(-1L, (byte) 3);
        result = NumberHelpers.shiftMS(-1L, (byte) 4);

        result = NumberHelpers.insertBitsIntoLong(-1L, (byte) 60,0b0111110L, (byte) 7);
        long resultextract = NumberHelpers.extractBitsFromLong(result, (byte) 60, (byte) 7);

        long bytePos = NumberHelpers.bitToIncludedBytePos(9);
        byte shiftAmount = NumberHelpers.remainderBits(9);
        int x = 5;
    }
    
    
}