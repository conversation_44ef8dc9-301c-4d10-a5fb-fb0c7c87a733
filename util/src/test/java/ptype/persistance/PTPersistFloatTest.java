package ptype.persistance;

import buffer.mem.localheap.LocalHeapBaseBufferRW;
import org.junit.jupiter.api.Test;
import ptype.def.builder.PDefFloatBuilder;
import ptype.def.typedef.PDefFloat;
import ptype.types.PTFloat;

public class PTPersistFloatTest {
    @Test
    void FloatSimple() {
        PTFloat ptFloat = new PTFloat(3.14f);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);

        ptFloat.writeObject(buffer,0L);

        PTFloat ptFloat2 = new PTFloat(0.0f);
        ptFloat2.readObject(buffer,0L);

        assert(ptFloat2.getValue() == 3.14f);
    }

    @Test
    void FloatSimple2() {
        PTFloat ptFloat = new PTFloat(2.718f);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);
        ptFloat.writeObject(buffer,0L);
        ptFloat.setValue(1.414f);
        ptFloat.writeObject(buffer,ptFloat.getWriteSize());

        PTFloat checkFloat = new PTFloat(0.0f);
        checkFloat.readObject(buffer,0L);
        assert(checkFloat.getValue() == 2.718f);

        checkFloat.readObject(buffer,checkFloat.getWriteSize());
        assert(checkFloat.getValue() == 1.414f);
    }

    @Test
    void FloatSimple3() {
        PDefFloat floatDef = new PDefFloatBuilder().Build();

        PTFloat ptFloat = new PTFloat(floatDef, 9.806f);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);
        ptFloat.writeObject(buffer,0L);
        ptFloat.setValue(6.626f);
        ptFloat.writeObject(buffer,ptFloat.getWriteSize());

        PTFloat checkFloat = new PTFloat(floatDef, 0.0f);
        checkFloat.readObject(buffer,0L);
        assert(checkFloat.getValue() == 9.806f);

        checkFloat.readObject(buffer,checkFloat.getWriteSize());
        assert(checkFloat.getValue() == 6.626f);
    }
}
