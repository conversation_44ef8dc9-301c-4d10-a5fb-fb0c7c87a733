package ptype.persistance;

import buffer.mem.localheap.LocalHeapBaseBufferRW;
import org.junit.jupiter.api.Test;
import ptype.def.builder.PDefDoubleBuilder;
import ptype.def.typedef.PDefDouble;
import ptype.types.PTDouble;

public class PTPersistDoubleTest {
    @Test
    void DoubleSimple() {
        PTDouble ptDouble = new PTDouble(3.14159);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);

        ptDouble.writeObject(buffer,0L);

        PTDouble ptDouble2 = new PTDouble(0.0);
        ptDouble2.readObject(buffer,0L);

        assert(ptDouble2.getValue() == 3.14159);
    }

    @Test
    void DoubleSimple2() {
        PTDouble ptDouble = new PTDouble(2.71828);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);
        ptDouble.writeObject(buffer,0L);
        ptDouble.setValue(1.41421);
        ptDouble.writeObject(buffer,ptDouble.getWriteSize());

        PTDouble checkDouble = new PTDouble(0.0);
        checkDouble.readObject(buffer,0L);
        assert(checkDouble.getValue() == 2.71828);

        checkDouble.readObject(buffer,checkDouble.getWriteSize());
        assert(checkDouble.getValue() == 1.41421);
    }

    @Test
    void DoubleSimple3() {
        PDefDouble doubleDef = new PDefDoubleBuilder().Build();

        PTDouble ptDouble = new PTDouble(doubleDef, 9.80665);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);
        ptDouble.writeObject(buffer,0L);
        ptDouble.setValue(6.62607);
        ptDouble.writeObject(buffer,ptDouble.getWriteSize());

        PTDouble checkDouble = new PTDouble(doubleDef, 0.0);
        checkDouble.readObject(buffer,0L);
        assert(checkDouble.getValue() == 9.80665);

        checkDouble.readObject(buffer,checkDouble.getWriteSize());
        assert(checkDouble.getValue() == 6.62607);
    }
}
