package ptype.persistance;

import buffer.mem.localheap.LocalHeapBaseBufferRW;
import org.junit.jupiter.api.Test;
import ptype.def.builder.PDefIntegerBuilder;
import ptype.def.typedef.PDefInteger;
import ptype.types.PTInteger;

public class PTPersistIntegerTest {
    @Test
    void IntSimple() {
        PTInteger ptInt = new PTInteger(42);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);

        ptInt.writeObject(buffer,0L);

        PTInteger ptInt2 = new PTInteger(0);
        ptInt2.readObject(buffer,0L);

        assert(ptInt2.getValue() == 42);
    }

    @Test
    void IntSimple2() {
        PTInteger ptInt = new PTInteger(42);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);
        ptInt.writeObject(buffer,0L);
        ptInt.setValue(44);
        ptInt.writeObject(buffer,ptInt.getWriteSize());

        PTInteger checkInt = new PTInteger(0);
        checkInt.readObject(buffer,0L);
        assert(checkInt.getValue() == 42);

        checkInt.readObject(buffer,checkInt.getWriteSize());
        assert(checkInt.getValue() == 44);

    }

    @Test
    void IntSimple3() {
        PDefInteger intDef = new PDefIntegerBuilder().addRange(-50, 50).Build();

        PTInteger ptInt = new PTInteger(intDef,42);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);
        ptInt.writeObject(buffer,0L);
        ptInt.setValue(44);
        ptInt.writeObject(buffer,ptInt.getWriteSize());

        PTInteger checkInt = new PTInteger(intDef,0);
        checkInt.readObject(buffer,0L);
        assert(checkInt.getValue() == 42);

        checkInt.readObject(buffer,checkInt.getWriteSize());
        assert(checkInt.getValue() == 44);

    }


}
