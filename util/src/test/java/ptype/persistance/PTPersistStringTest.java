package ptype.persistance;

import buffer.mem.localheap.LocalHeapBaseBufferRW;
import org.junit.jupiter.api.Test;
import ptype.def.builder.PDefStringBuilder;
import ptype.def.typedef.PDefString;
import ptype.types.PTString;

public class PTPersistStringTest {
    @Test
    void StringSimple() {
        PTString ptString = new PTString("Hello World");
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);

        ptString.writeObject(buffer,0L);

        PTString ptString2 = new PTString("");
        ptString2.readObject(buffer,0L);

        assert(ptString2.getAsString().equals("Hello World"));
    }

    @Test
    void StringSimple2() {
        PTString ptString = new PTString("First String");
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);
        ptString.writeObject(buffer,0L);
        ptString.setFromString("Second String");
        ptString.writeObject(buffer,ptString.getWriteSize());

        PTString checkString = new PTString("");
        checkString.readObject(buffer,0L);
        assert(checkString.getAsString().equals("First String"));

        checkString.readObject(buffer,checkString.getWriteSize());
        assert(checkString.getAsString().equals("Second String"));
    }

    @Test
    void StringSimple3() {
        PDefString stringDef = new PDefStringBuilder().Build();

        PTString ptString = new PTString(stringDef, "Initial Value");
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);
        ptString.writeObject(buffer,0L);
        ptString.setFromString("Updated Value");
        ptString.writeObject(buffer,ptString.getWriteSize());

        PTString checkString = new PTString(stringDef, "");
        checkString.readObject(buffer,0L);
        assert(checkString.getAsString().equals("Initial Value"));

        checkString.readObject(buffer,checkString.getWriteSize());
        assert(checkString.getAsString().equals("Updated Value"));
    }
}
