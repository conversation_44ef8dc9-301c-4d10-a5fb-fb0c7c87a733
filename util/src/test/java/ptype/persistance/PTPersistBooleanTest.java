package ptype.persistance;

import buffer.mem.localheap.LocalHeapBaseBufferRW;
import org.junit.jupiter.api.Test;
import ptype.def.builder.PDefBooleanBuilder;
import ptype.def.typedef.PDefBoolean;
import ptype.types.PTBoolean;

public class PTPersistBooleanTest {
    @Test
    void BooleanSimple() {
        PTBoolean ptBool = new PTBoolean(true);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);

        ptBool.writeObject(buffer,0L);

        PTBoolean ptBool2 = new PTBoolean(false);
        ptBool2.readObject(buffer,0L);


        assert(ptBool2.getAsBoolean());
    }

    @Test
    void BooleanSimple2() {
        PTBoolean ptBool = new PTBoolean(true);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);
        ptBool.writeObject(buffer,0L);
        ptBool.setFromBoolean(false);
        ptBool.writeObject(buffer,ptBool.getWriteSize());

        PTBoolean checkBool = new PTBoolean(true);
        checkBool.readObject(buffer,0L);
        assert(checkBool.getAsBoolean() );

        checkBool.readObject(buffer,checkBool.getWriteSize());
        assert(!checkBool.getAsBoolean() );

    }

    @Test
    void BooleanSimple3() {
        PDefBoolean boolDef = new PDefBooleanBuilder().Build();

        PTBoolean ptBool = new PTBoolean(boolDef,true);
        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);
        ptBool.writeObject(buffer,0L);
        ptBool.setFromBoolean(false);
        ptBool.writeObject(buffer,ptBool.getWriteSize());

        PTBoolean checkBool = new PTBoolean(boolDef,true);
        checkBool.readObject(buffer,0L);
        assert(checkBool.getAsBoolean() );

        checkBool.readObject(buffer,checkBool.getWriteSize());
        assert(!checkBool.getAsBoolean());

    }


}
