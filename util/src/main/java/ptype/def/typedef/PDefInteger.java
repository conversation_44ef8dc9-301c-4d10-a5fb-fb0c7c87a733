package ptype.def.typedef;

import javaHelpers.ASSERT;
import javaHelpers.NumberHelpers;
import ptype.types.PTInteger;

public class PDefInteger extends TypeDef<PTInteger>{
    private final long minValue;
    private final long maxValue;

    public PDefInteger(int numBits) {
        this.maxValue = NumberHelpers.maskLS((byte)(numBits -1));
        this.minValue = -(this.maxValue + 1);
    }
    public PDefInteger( long minValue, long maxValue) {
        this.minValue = minValue;
        this.maxValue = maxValue;
    }

    public byte getNumberBits(){
        return NumberHelpers.getNumberBitsNeeded(this.minValue,this.maxValue);
    }

    @Override
    public boolean isFixedSize() {
        return true;
    }

    @Override
    public PTInteger createNewType() {
        return new PTInteger(this);
    }

    @Override
    public boolean isValid(PTInteger ptInteger) {
        return true;
    }

    public static final PDefInteger DefaultDef = new PDefInteger(Long.MIN_VALUE,Long.MAX_VALUE);
}
