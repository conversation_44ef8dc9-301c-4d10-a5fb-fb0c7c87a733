package ptype.def.builder;

import ptype.def.typedef.PDefInteger;
import ptype.types.PTInteger;

public class PDefIntegerBuilder extends PDefBuilder<PTInteger>{
    private int minValue;
    private int maxValue;

    @Override
    public PDefInteger Build() {
        return new PDefInteger(minValue, maxValue); // Using default 64-bit size
    }

    public PDefIntegerBuilder addRange(int minValue, int maxValue) {
        this.minValue = minValue;
        this.maxValue = maxValue;
        return this;
    }
}
